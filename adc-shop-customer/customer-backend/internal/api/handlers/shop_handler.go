package handlers

import (
	"customer-backend/internal/services"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ShopHandler handles shop-related HTTP requests
type ShopHandler struct {
	shopService *services.ShopService
	logger      *logger.Logger
}

// NewShopHandler creates a new shop handler
func NewShopHandler(shopService *services.ShopService, logger *logger.Logger) *ShopHandler {
	return &ShopHandler{
		shopService: shopService,
		logger:      logger,
	}
}

// GetShops godoc
// @Summary Get shops list
// @Description Get a paginated list of shops with optional filters
// @Tags shops
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search query"
// @Param cuisine_type query []string false "Cuisine types"
// @Param price_range query []string false "Price ranges"
// @Param min_rating query number false "Minimum rating"
// @Param is_open query bool false "Filter by open status"
// @Param latitude query number false "Latitude for location-based search"
// @Param longitude query number false "Longitude for location-based search"
// @Param radius query number false "Search radius in kilometers"
// @Success 200 {object} types.ShopListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops [get]
func (h *ShopHandler) GetShops(c *gin.Context) {
	var filters types.ShopFilters

	// Apply defaults before binding
	filters.CustomerPagination.ApplyDefaults()
	filters.CustomerSorting.ApplyDefaults()

	// Debug: Log all query parameters before binding
	h.logger.WithField("all_query_params", c.Request.URL.Query()).Info("Received query parameters")

	// Bind query parameters (now without validation tags)
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).WithField("query", c.Request.URL.RawQuery).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse(fmt.Sprintf("Invalid query parameters: %v", err)))
		return
	}

	// Debug: Log filters after binding but before parseArrayFilters
	h.logger.WithFields(map[string]any{
		"search_after_bind":       filters.Search,
		"min_rating_after_bind":   filters.MinRating,
		"cuisine_type_after_bind": filters.CuisineType,
		"price_range_after_bind":  filters.PriceRange,
	}).Info("Filters after ShouldBindQuery")

	// Handle single value array parameters manually
	h.parseArrayFilters(c, &filters)

	// Apply defaults again in case binding overwrote with zero values
	filters.CustomerPagination.ApplyDefaults()
	filters.CustomerSorting.ApplyDefaults()

	// Debug logging
	h.logger.WithFields(map[string]any{
		"search":       filters.Search,
		"cuisine_type": filters.CuisineType,
		"price_range":  filters.PriceRange,
		"min_rating":   filters.MinRating,
		"page":         filters.Page,
		"limit":        filters.Limit,
		"query_params": c.Request.URL.RawQuery,
	}).Info("Processing shop filters")

	response, err := h.shopService.GetShops(c.Request.Context(), filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shops")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get shops"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetShop godoc
// @Summary Get shop details
// @Description Get detailed information about a specific shop
// @Tags shops
// @Accept json
// @Produce json
// @Param id path string true "Shop ID"
// @Success 200 {object} types.ShopDetailResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{id} [get]
func (h *ShopHandler) GetShop(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	shop, err := h.shopService.GetShopSettings(c.Request.Context(), shopID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop")
		c.JSON(http.StatusNotFound, types.CreateErrorResponse("Shop not found"))
		return
	}

	response := &types.ShopDetailResponse{}
	response.CustomerResponse = types.CreateCustomerResponse(
		struct {
			Shop types.CustomerShopSettings `json:"shop"`
		}{Shop: *shop},
		"Shop retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}

// GetNearbyShops godoc
// @Summary Get nearby shops
// @Description Get shops near a specific location
// @Tags shops
// @Accept json
// @Produce json
// @Param latitude query number true "Latitude"
// @Param longitude query number true "Longitude"
// @Param radius query number false "Search radius in kilometers" default(5)
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/nearby [get]
func (h *ShopHandler) GetNearbyShops(c *gin.Context) {
	latStr := c.Query("latitude")
	lngStr := c.Query("longitude")
	radiusStr := c.DefaultQuery("radius", "5")

	if latStr == "" || lngStr == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Latitude and longitude are required"))
		return
	}

	latitude, err := strconv.ParseFloat(latStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid latitude"))
		return
	}

	longitude, err := strconv.ParseFloat(lngStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid longitude"))
		return
	}

	radius, err := strconv.ParseFloat(radiusStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid radius"))
		return
	}

	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.shopService.GetNearbyShops(c.Request.Context(), latitude, longitude, radius, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get nearby shops")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get nearby shops"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// SearchShops godoc
// @Summary Search shops
// @Description Search for shops based on query string
// @Tags shops
// @Accept json
// @Produce json
// @Param q query string true "Search query"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/search [get]
func (h *ShopHandler) SearchShops(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Search query is required"))
		return
	}

	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.shopService.SearchShops(c.Request.Context(), query, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search shops")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to search shops"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetPopularShops godoc
// @Summary Get popular shops
// @Description Get a list of popular shops based on ratings
// @Tags shops
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopListResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/popular [get]
func (h *ShopHandler) GetPopularShops(c *gin.Context) {
	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.shopService.GetPopularShops(c.Request.Context(), filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular shops")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get popular shops"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetShopsByCategory godoc
// @Summary Get shops by category
// @Description Get shops filtered by cuisine category
// @Tags shops
// @Accept json
// @Produce json
// @Param category path string true "Cuisine category"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/category/{category} [get]
func (h *ShopHandler) GetShopsByCategory(c *gin.Context) {
	category := c.Param("category")
	if category == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Category is required"))
		return
	}

	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.shopService.GetShopsByCategory(c.Request.Context(), category, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shops by category")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get shops by category"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetShopFilterOptions godoc
// @Summary Get shop filter options
// @Description Get available filter options for shops (cuisine types, price ranges, features)
// @Tags shops
// @Accept json
// @Produce json
// @Success 200 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/filter-options [get]
func (h *ShopHandler) GetShopFilterOptions(c *gin.Context) {
	options, err := h.shopService.GetShopFilterOptions(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop filter options")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get shop filter options"))
		return
	}

	response := types.CustomerResponse{
		Success: true,
		Message: "Shop filter options retrieved successfully",
		Data:    options,
	}

	c.JSON(http.StatusOK, response)
}

// GetShopStatus godoc
// @Summary Get shop operating status
// @Description Check if a shop is currently open
// @Tags shops
// @Accept json
// @Produce json
// @Param id path string true "Shop ID"
// @Success 200 {object} types.CustomerResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{id}/status [get]
func (h *ShopHandler) GetShopStatus(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	isOpen, err := h.shopService.GetShopOperatingStatus(c.Request.Context(), shopID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop status")
		c.JSON(http.StatusNotFound, types.CreateErrorResponse("Shop not found"))
		return
	}

	response := types.CreateCustomerResponse(
		struct {
			IsOpen bool `json:"is_open"`
		}{IsOpen: isOpen},
		"Shop status retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}

// GetShopBySlug godoc
// @Summary Get shop details by slug
// @Description Get detailed information about a specific shop by slug
// @Tags shops
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Success 200 {object} types.ShopDetailResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug} [get]
func (h *ShopHandler) GetShopBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	shop, err := h.shopService.GetShopSettingsBySlug(c.Request.Context(), slug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop by slug")
		c.JSON(http.StatusNotFound, types.CreateErrorResponse("Shop not found"))
		return
	}

	response := &types.ShopDetailResponse{
		CustomerResponse: types.CustomerResponse{
			Success: true,
			Message: "Shop retrieved successfully",
			Meta: &types.ResponseMeta{
				Timestamp: time.Now(),
				Version:   "1.0",
			},
		},
		Data: struct {
			Shop types.CustomerShopSettings `json:"shop"`
		}{
			Shop: *shop,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetShopStatusBySlug godoc
// @Summary Get shop operating status by slug
// @Description Check if a shop is currently open by slug
// @Tags shops
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Success 200 {object} types.CustomerResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/status [get]
func (h *ShopHandler) GetShopStatusBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	isOpen, err := h.shopService.GetShopOperatingStatusBySlug(c.Request.Context(), slug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop status by slug")
		c.JSON(http.StatusNotFound, types.CreateErrorResponse("Shop not found"))
		return
	}

	response := types.CreateCustomerResponse(
		struct {
			IsOpen bool `json:"is_open"`
		}{IsOpen: isOpen},
		"Shop status retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}

// GetBranchBySlug retrieves branch information by shop slug and branch slug
// @Summary Get branch by slug
// @Description Get branch information using shop slug and branch slug
// @Tags shops
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Success 200 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/branches/slug/{branchSlug} [get]
func (h *ShopHandler) GetBranchBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	if branchSlug == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Branch slug is required"))
		return
	}

	branch, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, types.CreateErrorResponse("Branch not found"))
		return
	}

	response := types.CreateCustomerResponse(
		struct {
			Shop interface{} `json:"shop"`
		}{Shop: branch},
		"Branch retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}

// parseArrayFilters handles single value array parameters manually
// Gin's ShouldBindQuery doesn't automatically convert single values to arrays
func (h *ShopHandler) parseArrayFilters(c *gin.Context, filters *types.ShopFilters) {
	// Handle cuisine_type parameter
	if cuisineValues := c.QueryArray("cuisine_type"); len(cuisineValues) > 0 {
		filters.CuisineType = cuisineValues
	} else if cuisineValue := c.Query("cuisine_type"); cuisineValue != "" {
		filters.CuisineType = []string{cuisineValue}
	}

	// Handle price_range parameter
	if priceValues := c.QueryArray("price_range"); len(priceValues) > 0 {
		filters.PriceRange = priceValues
	} else if priceValue := c.Query("price_range"); priceValue != "" {
		filters.PriceRange = []string{priceValue}
	}
}
