"use client";

import { useState, useEffect, use<PERSON>emo, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CenterPagination, PaginationInfo } from "@/components/ui/pagination";
import { Search, MapPin, Star, Navigation } from "lucide-react";
import Header from "@/components/Header";
import { ShopFilters } from "@/lib/services/customerApiClient";
import { useFilteredPagination } from "@/lib/hooks/usePagination";
import { useGetShopsQuery, useGetShopFilterOptionsQuery } from "@/lib/store/api/customerApi";
import GoogleMapComponent from "@/components/GoogleMapReact";
import type { ShopLocation } from "@/components/GoogleMapReact";
import { geocodeAddress, reverseGeocode } from "@/lib/google-maps";

import Link from "next/link";
import { useRouter } from "next/navigation";
import DirectionsModal from "@/components/DirectionsModal";
import DistanceInfo from "@/components/DistanceInfo";



export default function HomePage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [locationQuery, setLocationQuery] = useState("");
  const [selectedCuisine, setSelectedCuisine] = useState<string>("all");
  const [selectedPriceRange, setSelectedPriceRange] = useState<string>("all");
  const [selectedRating, setSelectedRating] = useState<string>("all");
  const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number } | null>(null);

  // Directions modal state
  const [directionsModalOpen, setDirectionsModalOpen] = useState(false);
  const [selectedDestination, setSelectedDestination] = useState<{
    lat: number;
    lng: number;
    name: string;
    address: string;
  } | null>(null);

  // Fetch filter options
  const { data: filterOptionsResponse } = useGetShopFilterOptionsQuery();

  // Initialize pagination with filters
  const pagination = useFilteredPagination<ShopFilters>({
    initialPage: 1,
    initialLimit: 20,
    initialFilters: {
      search: "",
      cuisine_type: undefined,
      price_range: undefined,
      min_rating: undefined,
    },
  });

  // Prepare filters for RTK Query - use useMemo to ensure stable reference
  const filters: ShopFilters = useMemo(() => {
    const currentFilters: ShopFilters = {
      page: pagination.currentPage,
      limit: pagination.limit,
      search: searchQuery || undefined,
      cuisine_type: selectedCuisine !== "all" ? [selectedCuisine] : undefined,
      price_range: selectedPriceRange !== "all" ? [selectedPriceRange] : undefined,
      min_rating: selectedRating !== "all" ? parseFloat(selectedRating) : undefined,
    };

    // Debug logging
    console.log('Current filters:', currentFilters);

    return currentFilters;
  }, [
    pagination.currentPage,
    pagination.limit,
    searchQuery,
    selectedCuisine,
    selectedPriceRange,
    selectedRating,
  ]);

  // Use RTK Query to fetch shops
  const {
    data: shopsResponse,
    error,
    isLoading,
    refetch,
    isFetching,
  } = useGetShopsQuery(filters);

  // Debug logging for API calls
  useEffect(() => {
    console.log('RTK Query state:', {
      isLoading,
      isFetching,
      error,
      dataLength: shopsResponse?.data?.shops?.length || 0,
      filters,
    });

    // Log the complete response structure
    if (shopsResponse?.data?.shops) {
      console.log('Complete shops response:', JSON.stringify(shopsResponse.data.shops, null, 2));
    }
  }, [isLoading, isFetching, error, shopsResponse, filters]);

  // Extract data from response
  const shops = useMemo(() => shopsResponse?.data?.shops || [], [shopsResponse?.data?.shops]);

  // Helper function to get the correct URL for a shop (with branch if available)
  const getShopUrl = useCallback((item: { shopSlug: string; branchSlug: string }) => {
    return `/restaurant/${item.shopSlug}/${item.branchSlug}`;
  }, []);

  // Transform API data to include branch information
  // TEMPORARY: Add mock coordinates for branches that don't have them
  const shopBranches = useMemo(() => {
    if (!shops || shops.length === 0) return [];

    // Debug logging to see what shops data looks like
    console.log('Shops data:', shops);
    shops.forEach((shop, index) => {
      console.log(`Shop ${index}:`, {
        id: shop.id,
        name: shop.name,
        slug: shop.slug,
        branches: shop.branches,
        branchCount: shop.branches?.length || 0
      });
    });

    // Flatten shops and their branches into a single array
    const branches = shops.flatMap((shop, shopIndex) => {
      // Check if shop has branches
      if (!shop.branches || shop.branches.length === 0) {
        console.warn(`Shop ${shop.name} has no branches, skipping`);
        return [];
      }

      return shop.branches.map((branch, branchIndex) => {
        console.log(`Processing branch ${branchIndex} for shop ${shop.name}:`, branch);

        // Get coordinates - use branch coordinates if available, otherwise use shop coordinates
        let lat = branch.address?.latitude || shop.address?.latitude;
        let lng = branch.address?.longitude || shop.address?.longitude;

        console.log(`Branch ${branch.name} coordinates:`, {
          branchLat: branch.address?.latitude,
          branchLng: branch.address?.longitude,
          shopLat: shop.address?.latitude,
          shopLng: shop.address?.longitude,
          finalLat: lat,
          finalLng: lng
        });

        // TEMPORARY: If coordinates are still missing, generate some around the shop location or Bangkok
        if (!lat || !lng) {
          // If shop has coordinates, use them with larger offsets for branches
          if (shop.address?.latitude && shop.address?.longitude) {
            lat = shop.address.latitude + (branchIndex * 0.005); // Larger offset for each branch
            lng = shop.address.longitude + (branchIndex * 0.005);
            console.warn(`Using shop coordinates with offset for branch ${branch.name}: ${lat}, ${lng}`);
          } else {
            // Generate coordinates around Bangkok with small offsets
            lat = 13.7563 + (shopIndex * 0.01) + (branchIndex * 0.005);
            lng = 100.5018 + (shopIndex * 0.01) + (branchIndex * 0.005);
            console.warn(`Generated mock coordinates for ${branch.name}: ${lat}, ${lng}`);
          }
        }

        return {
          id: branch.id,
          shopId: shop.id,
          shopSlug: shop.slug,
          branchSlug: branch.slug,
          name: branch.name,
          shopName: shop.name,
          image: shop.logo,
          coverImage: shop.cover_image,
          description: shop.description,
          cuisineType: shop.cuisine_type,
          priceRange: shop.price_range,
          rating: shop.rating,
          lat: lat,
          lng: lng,
          address: branch.address?.street ?
                    `${branch.address.street}, ${branch.address.city || ''}` :
                    (shop.address?.street ?
                      `${shop.address.street}, ${shop.address.city || ''}` :
                      'Address not available'),
          businessHours: branch.business_hours || shop.business_hours,
          isOpen: shop.is_open,
        };
      });
    });

    console.log(`Found ${branches.length} branches with coordinates`);
    console.log('Processed branches:', branches);

    // Summary log
    console.log('Branch processing summary:', {
      totalShops: shops.length,
      totalBranches: branches.length,
      branchesPerShop: shops.map(shop => ({
        shopName: shop.name,
        branchCount: shop.branches?.length || 0,
        branches: shop.branches?.map(b => b.name) || []
      }))
    });

    return branches;
  }, [shops]);

  // Update pagination info when data changes
  useEffect(() => {
    if (shopsResponse?.pagination) {
      pagination.setPaginationInfo(shopsResponse.pagination);
    }
  }, [shopsResponse?.pagination, pagination]);

  // Get user location on component mount
  useEffect(() => {
    const getUserLocation = async () => {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject);
        });
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        setMapCenter(location);
      } catch (error) {
        console.warn('Could not get user location:', error);
        // Default to Bangkok, Thailand
        const defaultLocation = { lat: 13.7563, lng: 100.5018 };
        setMapCenter(defaultLocation);
      }
    };

    getUserLocation();
  }, []);

  // Handle search with debouncing
  const handleSearch = useCallback((query: string) => {
    console.log('Search query changed:', query);
    setSearchQuery(query);
    pagination.setPage(1);
  }, [pagination]);

  // Handle location search with geocoding
  const handleLocationSearch = async (query: string) => {
    setLocationQuery(query);
    if (query.trim() && query.length > 2) { // Only search if query is meaningful
      try {
        console.log('Searching for address:', query);
        const location = await geocodeAddress(query);
        if (location) {
          const newCenter = { lat: location.lat(), lng: location.lng() };
          console.log('Setting map center to:', newCenter);
          setMapCenter(newCenter);
        } else {
          console.warn('No location found for:', query);
        }
      } catch (error) {
        console.warn('Geocoding failed for query:', query, error);
        // You could show a toast notification here in the future
      }
    }
  };

  // Handle shop selection from map
  const handleShopClick = (shop: ShopLocation) => {
    // Navigate to shop detail page using Next.js router
    // Use the helper function to get the correct URL (with branch if available)
    if ('shopSlug' in shop && 'branchSlug' in shop) {
      const url = getShopUrl(shop as { shopSlug: string; branchSlug: string });
      router.push(url);
    } else {
      console.warn('Shop does not have required slug properties:', shop);
    }
  };

  // Handle map click for location selection with reverse geocoding
  const handleMapClick = async (lat: number, lng: number) => {
    setMapCenter({ lat, lng });
    try {
      const address = await reverseGeocode(lat, lng);
      if (address) {
        setLocationQuery(address);
      }
    } catch (error) {
      console.warn('Reverse geocoding failed:', error);
    }
  };

  // Handle "Find My Location" button
  const handleFindMyLocation = async () => {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject);
      });
      const location = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setMapCenter(location);
    } catch (error) {
      console.error('Could not get user location:', error);
      alert('Unable to get your location. Please check your browser permissions.');
    }
  };

  // Handle directions request
  const handleGetDirections = (shop: ShopLocation) => {
    setSelectedDestination({
      lat: shop.lat,
      lng: shop.lng,
      name: shop.name,
      address: shop.address,
    });
    setDirectionsModalOpen(true);
  };

  // Filter handlers
  const handleCuisineChange = useCallback((value: string) => {
    console.log('Cuisine filter changed:', value);
    setSelectedCuisine(value);
    pagination.setPage(1);
  }, [pagination]);

  const handlePriceRangeChange = useCallback((value: string) => {
    console.log('Price range filter changed:', value);
    setSelectedPriceRange(value);
    pagination.setPage(1);
  }, [pagination]);

  const handleRatingChange = useCallback((value: string) => {
    console.log('Rating filter changed:', value);
    setSelectedRating(value);
    pagination.setPage(1);
  }, [pagination]);

  // Extract filter options from API response
  const filterOptions = useMemo(() => {
    const options = filterOptionsResponse?.data;
    return {
      cuisineTypes: options?.cuisine_types || [
        "Italian", "Chinese", "Japanese", "Mexican", "Indian",
        "Thai", "American", "French", "Mediterranean", "Korean"
      ],
      priceRanges: options?.price_ranges || ["$", "$$", "$$$", "$$$$"],
      features: options?.features || []
    };
  }, [filterOptionsResponse]);

  if (isLoading && shopBranches.length === 0) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading restaurants...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    const errorMessage = 'status' in error
      ? `Error ${error.status}: Failed to load restaurants`
      : error.message || 'Failed to load restaurants';

    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <p className="text-destructive mb-4">{errorMessage}</p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="space-y-6">

            {/* Hero Section with Search */}
            <Card className="bg-gradient-to-r from-orange-50 to-amber-50 border-orange-200">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h1 className="text-4xl font-bold text-gray-900 mb-4">
                    Discover Restaurants Near You
                  </h1>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Find and explore restaurant branches in your area with our interactive map and detailed listings
                  </p>
                </div>

                {/* Search Bars */}
                <div className="space-y-4 mb-6 max-w-2xl mx-auto">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search for restaurants or cuisines"
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 h-12"
                  />
                </div>

                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Enter an address (e.g., Siam Paragon, Bangkok)"
                    value={locationQuery}
                    onChange={(e) => handleLocationSearch(e.target.value)}
                    className="pl-10 h-12"
                  />
                </div>
              </div>

              {/* Interactive Google Map - Updated to match map page style */}
              <div className="w-full h-96 rounded-lg mb-6 relative overflow-hidden border shadow-lg">
                <GoogleMapComponent
                  shops={shopBranches}
                  center={mapCenter || undefined}
                  zoom={13}
                  height="384px"
                  width="100%"
                  showUserLocation={true}
                  onShopClick={handleShopClick}
                  onMapClick={handleMapClick}
                  className="rounded-lg"
                />
                {/* Find My Location Button */}
                <div className="absolute top-4 right-4">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={handleFindMyLocation}
                    className="bg-white/90 hover:bg-white shadow-md"
                    title="Find my location"
                  >
                    <Navigation className="h-4 w-4" />
                  </Button>
                </div>

                {/* Map Statistics Overlay */}
                <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-md">
                  <p className="text-sm font-medium text-gray-800">
                    {shopBranches.length} branch{shopBranches.length !== 1 ? 'es' : ''} from {shops.length} restaurant{shops.length !== 1 ? 's' : ''}
                  </p>
                </div>
              </div>
              </CardContent>
            </Card>

            {/* Debug Info Panel */}
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-blue-800 text-sm">Debug Info</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-blue-700">
                  <p><strong>Shops loaded:</strong> {shops.length}</p>
                  <p><strong>Branches processed:</strong> {shopBranches.length}</p>
                  <div className="mt-2">
                    <strong>Shop details:</strong>
                    <ul className="list-disc list-inside ml-2">
                      {shops.map((shop) => (
                        <li key={shop.id}>
                          {shop.name}: {shop.branches?.length || 0} branches
                          {shop.branches?.map(branch => (
                            <span key={branch.id} className="ml-2 text-xs">
                              • {branch.name}
                            </span>
                          ))}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Filters Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  Restaurant Branches Near You ({shopBranches.length} branches from {shops.length} restaurants)
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Filter Dropdowns */}
                <div className="flex gap-3 flex-wrap">
              <div className="w-[180px]">
                <Select value={selectedCuisine} onValueChange={handleCuisineChange}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Cuisine" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Cuisines</SelectItem>
                    {filterOptions.cuisineTypes.map((cuisine) => (
                      <SelectItem key={cuisine} value={cuisine}>
                        {cuisine}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-[180px]">
                <Select value={selectedPriceRange} onValueChange={handlePriceRangeChange}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Price Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Prices</SelectItem>
                    {filterOptions.priceRanges.map((range) => (
                      <SelectItem key={range} value={range}>
                        {range}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-[180px]">
                <Select value={selectedRating} onValueChange={handleRatingChange}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Ratings" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Ratings</SelectItem>
                    <SelectItem value="4.5">4.5+ Stars</SelectItem>
                    <SelectItem value="4.0">4.0+ Stars</SelectItem>
                    <SelectItem value="3.5">3.5+ Stars</SelectItem>
                    <SelectItem value="3.0">3.0+ Stars</SelectItem>
                  </SelectContent>
                </Select>
              </div>
                </div>
              </CardContent>
            </Card>

            {/* Pagination Info */}
            <div className="px-4 pb-3">
              <PaginationInfo
                startItem={pagination.startItem}
                endItem={pagination.endItem}
                totalItems={pagination.totalItems}
              />
            </div>

            {/* Restaurant Branch List */}
            {shopBranches.map((branch) => (
              <div key={branch.id} className="p-4">
                <Card className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="flex items-center gap-4 p-4">
                    <div
                      className="w-16 h-16 bg-center bg-no-repeat bg-cover rounded-lg flex-shrink-0"
                      style={{
                        backgroundImage: branch.image ? `url("${branch.image}")` : 'none',
                        backgroundColor: branch.image ? 'transparent' : '#e5ccb2'
                      }}
                    />
                    <div className="flex-1 min-w-0">
                      <Link href={getShopUrl(branch)} className="block">
                        <CardHeader className="p-0">
                          <CardTitle className="text-lg font-semibold text-foreground hover:text-orange-600 transition-colors">
                            {branch.name} - {branch.shopName}
                          </CardTitle>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span>{branch.rating || "4.5"}</span>
                            </div>
                            <span>•</span>
                            <span>{branch.cuisineType || "Italian"}</span>
                            <span>•</span>
                            <span>{branch.priceRange || "$$"}</span>
                          </div>
                          {branch.description && (
                            <CardDescription className="text-sm mt-1">
                              {branch.description}
                            </CardDescription>
                          )}

                          {/* Distance and Time Info */}
                          <div className="mt-2">
                            <DistanceInfo
                              destination={{ lat: branch.lat, lng: branch.lng }}
                              className="mt-1"
                            />
                          </div>
                        </CardHeader>
                      </Link>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col gap-2 flex-shrink-0">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleGetDirections(branch);
                        }}
                        className="flex items-center gap-2 text-xs"
                      >
                        <Navigation className="h-3 w-3" />
                        Directions
                      </Button>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          router.push(getShopUrl(branch));
                        }}
                        className="bg-orange-500 hover:bg-orange-600 text-xs"
                      >
                        View Menu
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            ))}

            {/* Center Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-4 py-6">
                <CenterPagination
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  centerPages={pagination.centerPages}
                  showFirst={pagination.showFirst}
                  showLast={pagination.showLast}
                  onPageChange={pagination.setPage}
                  canGoPrev={pagination.canGoPrev}
                  canGoNext={pagination.canGoNext}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Directions Modal */}
      {selectedDestination && (
        <DirectionsModal
          isOpen={directionsModalOpen}
          onClose={() => setDirectionsModalOpen(false)}
          destination={selectedDestination}
        />
      )}
    </div>
  );
}
