'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Search, MapPin, Navigation, Filter, Star } from 'lucide-react';
import GoogleMap from './GoogleMap';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import {
  ShopLocation,
  getCurrentLocation,
  calculateDistance,
  formatDistance,
  geocodeAddress,
  convertCustomerShopToShopLocation
} from '@/lib/google-maps';
import { useGetShopsQuery, useGetNearbyShopsQuery } from '@/lib/store/api/customerApi';

interface ShopLocatorProps {
  onShopSelect?: (shop: ShopLocation) => void;
  className?: string;
}

const ShopLocator: React.FC<ShopLocatorProps> = ({ onShopSelect, className = '' }) => {
  const [shops, setShops] = useState<ShopLocation[]>([]);
  const [filteredShops, setFilteredShops] = useState<ShopLocation[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [selectedShop, setSelectedShop] = useState<ShopLocation | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [sortBy, setSortBy] = useState<'distance' | 'rating' | 'name'>('distance');
  const [filterOpen, setFilterOpen] = useState(false);
  const [cuisineFilter, setCuisineFilter] = useState<string>('');
  const [priceFilter, setPriceFilter] = useState<string>('');
  const [useNearbySearch, setUseNearbySearch] = useState(false);

  // API queries
  const {
    data: shopsResponse,
    error: shopsError,
    isLoading: isLoadingShops,
    refetch: refetchShops
  } = useGetShopsQuery({
    search: searchQuery || undefined,
    cuisine_type: cuisineFilter || undefined,
    price_range: priceFilter || undefined,
    is_active: true,
    limit: 50
  }, {
    skip: useNearbySearch // Skip general query when using nearby search
  });

  const {
    data: nearbyShopsResponse,
    error: nearbyShopsError,
    isLoading: isLoadingNearbyShops,
    refetch: refetchNearbyShops
  } = useGetNearbyShopsQuery({
    latitude: userLocation?.lat || 0,
    longitude: userLocation?.lng || 0,
    radius: 10, // 10km radius
    filters: {
      search: searchQuery || undefined,
      cuisine_type: cuisineFilter || undefined,
      price_range: priceFilter || undefined,
      is_active: true,
      limit: 50
    }
  }, {
    skip: !useNearbySearch || !userLocation // Skip nearby query when not using nearby search or no location
  });

  // Process API data into ShopLocation format
  const processShopsData = useCallback((apiShops: any[]): ShopLocation[] => {
    if (!apiShops) return [];

    const validShops = apiShops
      .map(convertCustomerShopToShopLocation)
      .filter((shop): shop is ShopLocation => shop !== null);

    // Calculate distances if user location is available
    if (userLocation) {
      return validShops.map(shop => ({
        ...shop,
        distance: calculateDistance(userLocation, { lat: shop.lat, lng: shop.lng }),
      }));
    }

    return validShops;
  }, [userLocation]);

  // Get user location
  const getUserLocation = useCallback(async () => {
    setIsLoadingLocation(true);
    try {
      const position = await getCurrentLocation();
      const location = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setUserLocation(location);

      // Try nearby search, but don't switch immediately - let the effect handle fallback
      setUseNearbySearch(true);
    } catch (error) {
      console.error('Error getting location:', error);
      // Fallback to general search if location fails
      setUseNearbySearch(false);
    } finally {
      setIsLoadingLocation(false);
    }
  }, []);

  // Search shops by address
  const searchByAddress = useCallback(async (address: string) => {
    if (!address.trim()) {
      setSearchQuery('');
      return;
    }

    try {
      const location = await geocodeAddress(address);
      if (location) {
        setUserLocation({ lat: location.lat(), lng: location.lng() });
        setUseNearbySearch(true); // Switch to nearby search with new location
      } else {
        // If geocoding fails, treat as text search
        setSearchQuery(address);
        setUseNearbySearch(false);
      }
    } catch (error) {
      console.error('Error searching address:', error);
      // Fallback to text search
      setSearchQuery(address);
      setUseNearbySearch(false);
    }
  }, []);

  // Filter and sort shops (client-side filtering for additional refinement)
  const filterAndSortShops = useCallback(() => {
    let filtered = [...shops];

    // Sort shops (API handles most filtering, this is for sorting)
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          return (a.distance || Infinity) - (b.distance || Infinity);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

    setFilteredShops(filtered);
  }, [shops, sortBy]);

  // Handle shop selection
  const handleShopSelect = useCallback((shop: ShopLocation) => {
    setSelectedShop(shop);
    if (onShopSelect) {
      onShopSelect(shop);
    }
  }, [onShopSelect]);

  // Get unique cuisine types and price ranges for filters
  const cuisineTypes = [...new Set(shops.map(shop => shop.cuisineType).filter(Boolean))];
  const priceRanges = [...new Set(shops.map(shop => shop.priceRange).filter(Boolean))];

  // Loading and error states
  const isLoading = isLoadingShops || isLoadingNearbyShops || isLoadingLocation;
  const error = shopsError || nearbyShopsError;

  // Effects
  useEffect(() => {
    // Process API data when it changes
    const currentResponse = useNearbySearch ? nearbyShopsResponse : shopsResponse;

    // If nearby search fails, fall back to general search
    if (useNearbySearch && nearbyShopsError && shopsResponse?.data?.shops) {
      console.warn('Nearby search failed, falling back to general search');
      setUseNearbySearch(false);
      const processedShops = processShopsData(shopsResponse.data.shops);
      setShops(processedShops);
    } else if (currentResponse?.data?.shops) {
      const processedShops = processShopsData(currentResponse.data.shops);
      setShops(processedShops);
    }
  }, [shopsResponse, nearbyShopsResponse, nearbyShopsError, useNearbySearch, processShopsData]);

  useEffect(() => {
    filterAndSortShops();
  }, [filterAndSortShops]);

  useEffect(() => {
    // Auto-get user location on mount
    getUserLocation();
  }, [getUserLocation]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Find Restaurants Near You
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Bar */}
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search restaurants or enter address..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    searchByAddress(searchQuery);
                  }
                }}
                className="pl-10"
              />
            </div>
            <Button
              onClick={() => searchByAddress(searchQuery)}
              variant="outline"
              disabled={isLoading}
            >
              Search
            </Button>
            <Button
              onClick={getUserLocation}
              disabled={isLoadingLocation}
              variant="outline"
            >
              <Navigation className="w-4 h-4 mr-2" />
              {isLoadingLocation ? 'Locating...' : 'My Location'}
            </Button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-2 items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilterOpen(!filterOpen)}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'distance' | 'rating' | 'name')}
              className="px-3 py-1 border rounded text-sm"
            >
              <option value="distance">Sort by Distance</option>
              <option value="rating">Sort by Rating</option>
              <option value="name">Sort by Name</option>
            </select>

            {filterOpen && (
              <div className="flex gap-2">
                <select
                  value={cuisineFilter}
                  onChange={(e) => setCuisineFilter(e.target.value)}
                  className="px-3 py-1 border rounded text-sm"
                >
                  <option value="">All Cuisines</option>
                  {cuisineTypes.map(cuisine => (
                    <option key={cuisine} value={cuisine}>{cuisine}</option>
                  ))}
                </select>

                <select
                  value={priceFilter}
                  onChange={(e) => setPriceFilter(e.target.value)}
                  className="px-3 py-1 border rounded text-sm"
                >
                  <option value="">All Prices</option>
                  {priceRanges.map(price => (
                    <option key={price} value={price}>{price}</option>
                  ))}
                </select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-800">
              <MapPin className="w-5 h-5" />
              <span>Failed to load restaurants. Please try again.</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Map and Results */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Map */}
        <div className="order-2 lg:order-1">
          <GoogleMap
            shops={filteredShops}
            center={userLocation || undefined}
            height="500px"
            showUserLocation={true}
            onShopClick={handleShopSelect}
            className="rounded-lg border"
          />
        </div>

        {/* Shop List */}
        <div className="order-1 lg:order-2 space-y-3 max-h-[500px] overflow-y-auto">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-lg">
              {isLoading ? 'Loading...' : `${filteredShops.length} Restaurant${filteredShops.length !== 1 ? 's' : ''} Found`}
            </h3>
            {useNearbySearch && userLocation && (
              <Badge variant="outline" className="text-xs">
                Near your location
              </Badge>
            )}
          </div>

          {isLoading ? (
            // Loading state
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="flex items-center gap-4">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                    <div className="h-4 bg-gray-200 rounded w-12"></div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            filteredShops.map((shop) => (
              <Card
                key={shop.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedShop?.id === shop.id ? 'ring-2 ring-orange-500' : ''
                }`}
                onClick={() => handleShopSelect(shop)}
              >
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold text-lg">{shop.name}</h4>
                    {shop.isOpen !== undefined && (
                      <Badge variant={shop.isOpen ? "default" : "secondary"}>
                        {shop.isOpen ? 'Open' : 'Closed'}
                      </Badge>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 mb-2">{shop.address}</p>

                  <div className="flex items-center gap-4 text-sm">
                    {shop.rating && (
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{shop.rating.toFixed(1)}</span>
                        {shop.reviewCount && (
                          <span className="text-gray-500">({shop.reviewCount})</span>
                        )}
                      </div>
                    )}

                    {shop.cuisineType && (
                      <Badge variant="outline">{shop.cuisineType}</Badge>
                    )}

                    {shop.priceRange && (
                      <span className="text-gray-600">{shop.priceRange}</span>
                    )}

                    {shop.distance && (
                      <span className="text-gray-600">
                        {formatDistance(shop.distance)}
                      </span>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          )}

          {!isLoading && filteredShops.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>No restaurants found matching your criteria.</p>
              {error && (
                <p className="text-sm mt-2">Please check your connection and try again.</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShopLocator;
