'use client';

import React, { useState, useEffect } from 'react';
import { MapPin, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { 
  getCurrentLocation, 
  calculateDistance, 
  formatDistance 
} from '@/lib/google-maps';

interface DistanceInfoProps {
  destination: {
    lat: number;
    lng: number;
  };
  className?: string;
}

export default function DistanceInfo({ destination, className = '' }: DistanceInfoProps) {
  const [distance, setDistance] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const calculateDistanceFromUser = async () => {
      try {
        const position = await getCurrentLocation();
        const userLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        
        const dist = calculateDistance(userLocation, destination);
        setDistance(dist);
      } catch (error) {
        console.warn('Could not get user location for distance calculation:', error);
        // Don't show distance if we can't get user location
      } finally {
        setIsLoading(false);
      }
    };

    calculateDistanceFromUser();
  }, [destination]);

  // Estimate travel time based on distance (rough estimates)
  const getEstimatedTime = (distance: number): string => {
    // Rough estimates for Bangkok traffic
    if (distance < 1) {
      return '5-10 min'; // Walking distance
    } else if (distance < 5) {
      return '10-20 min'; // Short drive
    } else if (distance < 10) {
      return '20-35 min'; // Medium drive
    } else {
      return '35+ min'; // Long drive
    }
  };

  if (isLoading || distance === null) {
    return null; // Don't show anything if loading or no distance
  }

  return (
    <div className={`flex items-center gap-3 text-xs text-gray-600 ${className}`}>
      <div className="flex items-center gap-1">
        <MapPin className="h-3 w-3" />
        <span>{formatDistance(distance)}</span>
      </div>
      <div className="flex items-center gap-1">
        <Clock className="h-3 w-3" />
        <span>{getEstimatedTime(distance)}</span>
      </div>
    </div>
  );
}
