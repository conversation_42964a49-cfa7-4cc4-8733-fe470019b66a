'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Navigation, 
  MapPin, 
  Clock, 
  Route, 
  Car, 
  Bike, 
  PersonStanding,
  ExternalLink,
  Smartphone
} from 'lucide-react';
import { 
  getDirections, 
  openDirections, 
  getCurrentLocation,
  formatDistance,
  DirectionsResult 
} from '@/lib/google-maps';

interface DirectionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  destination: {
    lat: number;
    lng: number;
    name: string;
    address: string;
  };
}

export default function DirectionsModal({
  isOpen,
  onClose,
  destination
}: DirectionsModalProps) {
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [directions, setDirections] = useState<DirectionsResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [travelMode, setTravelMode] = useState<google.maps.TravelMode>(google.maps.TravelMode.DRIVING);

  // Get user location when modal opens
  useEffect(() => {
    if (isOpen && !userLocation) {
      getUserLocation();
    }
  }, [isOpen, userLocation]);

  // Get directions when user location and travel mode change
  useEffect(() => {
    if (userLocation && isOpen) {
      fetchDirections();
    }
  }, [userLocation, travelMode, isOpen]);

  const getUserLocation = async () => {
    try {
      const position = await getCurrentLocation();
      setUserLocation({
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      });
    } catch (error) {
      console.error('Error getting user location:', error);
      setError('Unable to get your location. Please enable location services.');
    }
  };

  const fetchDirections = async () => {
    if (!userLocation) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await getDirections(
        userLocation,
        { lat: destination.lat, lng: destination.lng },
        travelMode
      );
      setDirections(result);
    } catch (error) {
      console.error('Error getting directions:', error);
      setError('Unable to get directions. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenInMaps = () => {
    openDirections(
      { lat: destination.lat, lng: destination.lng },
      destination.name
    );
  };

  const getTravelModeIcon = (mode: google.maps.TravelMode) => {
    switch (mode) {
      case google.maps.TravelMode.DRIVING:
        return <Car className="h-4 w-4" />;
      case google.maps.TravelMode.BICYCLING:
        return <Bike className="h-4 w-4" />;
      case google.maps.TravelMode.WALKING:
        return <PersonStanding className="h-4 w-4" />;
      default:
        return <Car className="h-4 w-4" />;
    }
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.round(seconds / 60);
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const route = directions?.routes[0];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Navigation className="h-5 w-5" />
            Directions
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Destination Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-red-500 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold">{destination.name}</h3>
                  <p className="text-sm text-gray-600">{destination.address}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Travel Mode Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Travel Mode</label>
            <div className="flex gap-2">
              {[
                { mode: google.maps.TravelMode.DRIVING, label: 'Drive' },
                { mode: google.maps.TravelMode.WALKING, label: 'Walk' },
                { mode: google.maps.TravelMode.BICYCLING, label: 'Bike' },
              ].map(({ mode, label }) => (
                <Button
                  key={mode}
                  variant={travelMode === mode ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTravelMode(mode)}
                  className="flex items-center gap-2"
                >
                  {getTravelModeIcon(mode)}
                  {label}
                </Button>
              ))}
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
              <p className="text-sm text-gray-600 mt-2">Getting directions...</p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-4">
              <p className="text-sm text-red-600">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={getUserLocation}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          )}

          {/* Directions Summary */}
          {route && !isLoading && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Route className="h-4 w-4 text-blue-500" />
                    <span className="font-medium">Route Summary</span>
                  </div>
                  <Badge variant="secondary">
                    {getTravelModeIcon(travelMode)}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">Distance</p>
                      <p className="text-gray-600">{route.legs[0].distance?.text}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">Duration</p>
                      <p className="text-gray-600">{route.legs[0].duration?.text}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleOpenInMaps}
              className="flex-1 bg-orange-500 hover:bg-orange-600"
            >
              <Smartphone className="h-4 w-4 mr-2" />
              Open in Maps
            </Button>
            <Button
              variant="outline"
              onClick={onClose}
            >
              Close
            </Button>
          </div>

          {/* Helpful Note */}
          <p className="text-xs text-gray-500 text-center">
            Opens in your device's default maps app for turn-by-turn navigation
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
