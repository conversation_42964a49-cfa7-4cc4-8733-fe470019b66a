Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-06T10:55:29+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-06T10:55:29+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-06T10:55:30+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] POST   /api/v1/auth/login        --> customer-backend/internal/api/handlers.(*AuthHandler).Login-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/register     --> customer-backend/internal/api/handlers.(*AuthHandler).Register-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/oauth-user   --> customer-backend/internal/api/handlers.(*AuthHandler).CreateOAuthCustomer-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/logout       --> customer-backend/internal/api/handlers.(*AuthHandler).Logout-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/refresh      --> customer-backend/internal/api/handlers.(*AuthHandler).RefreshToken-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/forgot-password --> customer-backend/internal/api/handlers.(*AuthHandler).ForgotPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/reset-password --> customer-backend/internal/api/handlers.(*AuthHandler).ResetPassword-fm (6 handlers)
[GIN-debug] GET    /shops                    --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /shops/search             --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /shops/popular            --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /shops/nearby             --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/filter-options     --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /shops/:id                --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /shops/:id/status         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu           --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/popular   --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/new       --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/search    --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug --> customer-backend/internal/api/handlers.(*ShopHandler).GetBranchBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables --> customer-backend/internal/api/handlers.(*TableHandler).GetTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/available --> customer-backend/internal/api/handlers.(*TableHandler).GetAvailableTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/number/:tableNumber --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByNumber-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/:tableId/validate --> customer-backend/internal/api/handlers.(*TableHandler).ValidateTableForOrder-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/table-areas --> customer-backend/internal/api/handlers.(*TableHandler).GetTableAreas-fm (6 handlers)
[GIN-debug] GET    /menu/items/:itemId       --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /tables/:tableId          --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByID-fm (6 handlers)
[GIN-debug] POST   /orders/table             --> customer-backend/internal/api/handlers.(*OrderHandler).CreateTableOrder-fm (6 handlers)
[GIN-debug] POST   /orders/create-with-payment --> customer-backend/internal/api/handlers.(*OrderHandler).CreateOrderWithPayment-fm (6 handlers)
[GIN-debug] GET    /orders/:orderId          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByID-fm (6 handlers)
[GIN-debug] GET    /orders/number/:orderNumber --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByNumber-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId    --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId/active --> customer-backend/internal/api/handlers.(*OrderHandler).GetActiveOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/customer          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByCustomer-fm (6 handlers)
[GIN-debug] PUT    /orders/:orderId/status   --> customer-backend/internal/api/handlers.(*OrderHandler).UpdateOrderStatus-fm (6 handlers)
[GIN-debug] POST   /payments/create-intent   --> customer-backend/internal/api/handlers.(*PaymentHandler).CreatePaymentIntent-fm (6 handlers)
[GIN-debug] POST   /payments/confirm         --> customer-backend/internal/api/handlers.(*PaymentHandler).ConfirmPayment-fm (6 handlers)
[GIN-debug] GET    /payments/:paymentIntentId/status --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPaymentStatus-fm (6 handlers)
[GIN-debug] POST   /payments/:paymentIntentId/cancel --> customer-backend/internal/api/handlers.(*PaymentHandler).CancelPayment-fm (6 handlers)
[GIN-debug] POST   /payments/webhook         --> customer-backend/internal/api/handlers.(*PaymentHandler).HandleWebhook-fm (6 handlers)
[GIN-debug] GET    /payments/config          --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPublishableKey-fm (6 handlers)
[GIN-debug] GET    /cart                     --> customer-backend/internal/api/handlers.(*CartHandler).GetCart-fm (6 handlers)
[GIN-debug] POST   /cart/add                 --> customer-backend/internal/api/handlers.(*CartHandler).AddToCart-fm (6 handlers)
[GIN-debug] PUT    /cart/update              --> customer-backend/internal/api/handlers.(*CartHandler).UpdateQuantity-fm (6 handlers)
[GIN-debug] DELETE /cart/remove              --> customer-backend/internal/api/handlers.(*CartHandler).RemoveFromCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear               --> customer-backend/internal/api/handlers.(*CartHandler).ClearCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear-branch        --> customer-backend/internal/api/handlers.(*CartHandler).ClearBranchCart-fm (6 handlers)
[GIN-debug] POST   /cart/sync                --> customer-backend/internal/api/handlers.(*CartHandler).SyncCartOnLogin-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-06T10:55:30+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:55:40+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:55:40+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:55:40+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:55:40+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:55:40+07:00"}

2025/06/06 10:55:42 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[566.159ms] [33m[rows:0][35m SELECT * FROM "cart_items" WHERE "cart_items"."cart_session_id" = 'bb7f265b-4935-4c69-be81-65b9c2a2b2df' AND "cart_items"."deleted_at" IS NULL[0m

2025/06/06 10:55:42 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[1259.695ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749179554604_u453qxvol' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:55:42 +07] \"GET /cart HTTP/1.1 200 1.2599915s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:55:42+07:00"}

2025/06/06 10:55:43 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:158 [33mSLOW SQL >= 200ms
[0m[31;1m[2649.250ms] [33m[rows:0][35m SELECT * FROM "orders" WHERE customer_phone = 'guest_1749179554604_u453qxvol' AND "orders"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 50[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:55:43 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 2.650241625s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:55:43+07:00"}

2025/06/06 10:55:43 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:350 [33mSLOW SQL >= 200ms
[0m[31;1m[3000.171ms] [33m[rows:2][35m SELECT DISTINCT "cuisine_type" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 10:55:43 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:138 [33mSLOW SQL >= 200ms
[0m[31;1m[2992.713ms] [33m[rows:1][35m SELECT count(*) FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 10:55:44 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:375 [33mSLOW SQL >= 200ms
[0m[31;1m[271.100ms] [33m[rows:2][35m SELECT DISTINCT "price_range" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:55:44 +07] \"GET /shops/filter-options HTTP/1.1 200 3.272362375s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:55:44+07:00"}

2025/06/06 10:55:44 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[307.530ms] [33m[rows:5][35m SELECT * FROM "shop_branches" WHERE "shop_branches"."shop_id" IN ('550e8400-e29b-41d4-a716-446655440001','672023d9-8971-43fb-b09e-fe41bbb55ec7') AND is_active = true AND "shop_branches"."deleted_at" IS NULL[0m

2025/06/06 10:55:44 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[569.231ms] [33m[rows:2][35m SELECT * FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 20[0m
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:55:44+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:55:44+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:55:44 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 3.565908916s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:55:44+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: posriya, page=1, limit=20","time":"2025-06-06T10:57:24+07:00"}

2025/06/06 10:57:24 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:399 [33mSLOW SQL >= 200ms
[0m[31;1m[283.853ms] [33m[rows:1][35m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'posriya' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1[0m
{"level":"info","msg":"Looking for branch with shop slug: weerawat-poseeya, branch slug: posriya","time":"2025-06-06T10:57:24+07:00"}

2025/06/06 10:57:25 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:248 [33mSLOW SQL >= 200ms
[0m[31;1m[242.915ms] [33m[rows:1][35m SELECT shop_branches.id FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'posriya' AND shops.is_active = true AND shop_branches.is_active = true[0m

2025/06/06 10:57:25 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:321 [33mSLOW SQL >= 200ms
[0m[31;1m[299.028ms] [33m[rows:1][35m SELECT count(*) FROM "menu_items" WHERE (branch_id = 'c8143f1c-62f1-4a37-b312-c64b847203c6' AND is_available = true) AND is_available = true[0m

2025/06/06 10:57:31 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:158 [33mSLOW SQL >= 200ms
[0m[31;1m[1706.084ms] [33m[rows:0][35m SELECT * FROM "orders" WHERE customer_phone = 'guest_1749179554604_u453qxvol' AND "orders"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 50[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:57:31 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 1.707334708s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:57:31+07:00"}

2025/06/06 10:57:33 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[1090.832ms] [33m[rows:0][35m SELECT * FROM "cart_items" WHERE "cart_items"."cart_session_id" = 'bb7f265b-4935-4c69-be81-65b9c2a2b2df' AND "cart_items"."deleted_at" IS NULL[0m

2025/06/06 10:57:33 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[3286.792ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749179554604_u453qxvol' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:57:33 +07] \"GET /cart HTTP/1.1 200 3.286888625s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:57:33+07:00"}

2025/06/06 10:57:42 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:359 [33mSLOW SQL >= 200ms
[0m[31;1m[17448.302ms] [33m[rows:3][35m SELECT 
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name,
			menu_items.created_at,
			menu_items.updated_at
		 FROM "menu_items" LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id WHERE (menu_items.branch_id = 'c8143f1c-62f1-4a37-b312-c64b847203c6' AND menu_items.is_available = true) AND is_available = true ORDER BY menu_items.created_at DESC LIMIT 20[0m

2025/06/06 10:57:43 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:461 [33mSLOW SQL >= 200ms
[0m[31;1m[202.029ms] [33m[rows:0][35m SELECT * FROM "menu_categories" WHERE branch_id = 'c8143f1c-62f1-4a37-b312-c64b847203c6' AND is_active = true ORDER BY sort_order ASC, name ASC[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:57:43 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/posriya/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 18.906584708s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:57:43+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:00:13 +07] \"GET /api/v1/shops HTTP/1.1 404 28.167µs \"curl/8.7.1\" \"\n","time":"2025-06-06T11:00:13+07:00"}
{"all_query_params":{},"level":"info","msg":"Received query parameters","time":"2025-06-06T11:00:19+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T11:00:19+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"","search":"","time":"2025-06-06T11:00:19+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T11:00:19+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:00:19+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:00:19+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:00:19+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:00:19 +07] \"GET /shops HTTP/1.1 200 179.35875ms \"curl/8.7.1\" \"\n","time":"2025-06-06T11:00:19+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:00:33 +07] \"GET /cart HTTP/1.1 200 191.576666ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:00:33+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:00:33 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 127.138208ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:00:33+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:02:06 +07] \"GET /cart HTTP/1.1 200 132.28175ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:02:06+07:00"}
{"all_query_params":{"is_active":["true"],"limit":["50"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T11:02:06+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T11:02:06+07:00"}
{"cuisine_type":null,"level":"info","limit":50,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"is_active=true\u0026limit=50","search":"","time":"2025-06-06T11:02:06+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=50","time":"2025-06-06T11:02:06+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:02:06+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:02:06 +07] \"GET /cart HTTP/1.1 200 83.650625ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:02:06+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:02:06 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 75.847583ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:02:06+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:02:06+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:02:06+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:02:06 +07] \"GET /shops?is_active=true\u0026limit=50 HTTP/1.1 200 157.955ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:02:06+07:00"}
{"all_query_params":{"is_active":["true"],"limit":["50"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T11:05:03+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T11:05:03+07:00"}
{"cuisine_type":null,"level":"info","limit":50,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"is_active=true\u0026limit=50","search":"","time":"2025-06-06T11:05:03+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=50","time":"2025-06-06T11:05:03+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:05:03+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:05:03 +07] \"GET /cart HTTP/1.1 200 122.268583ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:05:03+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:05:03 +07] \"GET /orders/customer?customerPhone=guest_1749180759622_hcxcuumd3\u0026limit=50 HTTP/1.1 200 76.603667ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:05:03+07:00"}

2025/06/06 11:05:03 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[235.457ms] [33m[rows:2][35m SELECT * FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 50[0m
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:05:03+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:05:03+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:05:03 +07] \"GET /shops?is_active=true\u0026limit=50 HTTP/1.1 200 361.436667ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:05:03+07:00"}
{"all_query_params":{"is_active":["true"],"limit":["50"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T11:05:14+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T11:05:14+07:00"}
{"cuisine_type":null,"level":"info","limit":50,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"is_active=true\u0026limit=50","search":"","time":"2025-06-06T11:05:14+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=50","time":"2025-06-06T11:05:14+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:05:14+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:05:14+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:05:14+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:05:14 +07] \"GET /shops?is_active=true\u0026limit=50 HTTP/1.1 200 198.369084ms \"curl/8.7.1\" \"\n","time":"2025-06-06T11:05:14+07:00"}
{"all_query_params":{"is_active":["true"],"limit":["50"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T11:05:20+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T11:05:20+07:00"}
{"cuisine_type":null,"level":"info","limit":50,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"is_active=true\u0026limit=50","search":"","time":"2025-06-06T11:05:20+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=50","time":"2025-06-06T11:05:20+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:05:20+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:05:20+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:05:20+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:05:20 +07] \"GET /shops?is_active=true\u0026limit=50 HTTP/1.1 200 201.560083ms \"curl/8.7.1\" \"\n","time":"2025-06-06T11:05:20+07:00"}
{"level":"info","msg":"Getting nearby shops: lat=13.809047, lng=100.735385, radius=10.000000","time":"2025-06-06T11:05:26+07:00"}
{"level":"info","msg":"Getting shops with filters: page=0, limit=50","time":"2025-06-06T11:05:26+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:05:26+07:00"}

2025/06/06 11:05:27 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [35;1mERROR: column "distance" does not exist (SQLSTATE 42703)
[0m[33m[40.321ms] [34;1m[rows:1][0m SELECT * FROM "shops" WHERE is_active = true AND 
			(6371 * acos(cos(radians(13.8090471)) * cos(radians(address_latitude)) *
			cos(radians(address_longitude) - radians(100.7353852)) +
			sin(radians(13.8090471)) * sin(radians(address_latitude)))) <= 10 AND "shops"."deleted_at" IS NULL ORDER BY distance DESC LIMIT 50
{"error":"failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get shops","time":"2025-06-06T11:05:27+07:00"}
{"error":"failed to get shops: failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get nearby shops","time":"2025-06-06T11:05:27+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:05:27 +07] \"GET /shops/nearby?latitude=13.8090471\u0026longitude=100.7353852\u0026radius=10\u0026is_active=true\u0026limit=50 HTTP/1.1 500 162.615042ms \"curl/8.7.1\" \"\n","time":"2025-06-06T11:05:27+07:00"}
{"level":"info","msg":"Getting nearby shops: lat=13.809047, lng=100.735385, radius=50.000000","time":"2025-06-06T11:05:33+07:00"}
{"level":"info","msg":"Getting shops with filters: page=0, limit=50","time":"2025-06-06T11:05:33+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:05:33+07:00"}

2025/06/06 11:05:33 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [35;1mERROR: column "distance" does not exist (SQLSTATE 42703)
[0m[33m[39.988ms] [34;1m[rows:1][0m SELECT * FROM "shops" WHERE is_active = true AND 
			(6371 * acos(cos(radians(13.8090471)) * cos(radians(address_latitude)) *
			cos(radians(address_longitude) - radians(100.7353852)) +
			sin(radians(13.8090471)) * sin(radians(address_latitude)))) <= 50 AND "shops"."deleted_at" IS NULL ORDER BY distance DESC LIMIT 50
{"error":"failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get shops","time":"2025-06-06T11:05:33+07:00"}
{"error":"failed to get shops: failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get nearby shops","time":"2025-06-06T11:05:33+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:05:33 +07] \"GET /shops/nearby?latitude=13.8090471\u0026longitude=100.7353852\u0026radius=50\u0026is_active=true\u0026limit=50 HTTP/1.1 500 121.722083ms \"curl/8.7.1\" \"\n","time":"2025-06-06T11:05:33+07:00"}
{"level":"info","msg":"Getting nearby shops: lat=13.809047, lng=100.735385, radius=50.000000","time":"2025-06-06T11:05:39+07:00"}
{"level":"info","msg":"Getting shops with filters: page=0, limit=50","time":"2025-06-06T11:05:39+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:05:39+07:00"}

2025/06/06 11:05:39 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [35;1mERROR: column "distance" does not exist (SQLSTATE 42703)
[0m[33m[40.020ms] [34;1m[rows:1][0m SELECT * FROM "shops" WHERE is_active = true AND 
			(6371 * acos(cos(radians(13.8090471)) * cos(radians(address_latitude)) *
			cos(radians(address_longitude) - radians(100.7353852)) +
			sin(radians(13.8090471)) * sin(radians(address_latitude)))) <= 50 AND "shops"."deleted_at" IS NULL ORDER BY distance DESC LIMIT 50
{"error":"failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get shops","time":"2025-06-06T11:05:39+07:00"}
{"error":"failed to get shops: failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get nearby shops","time":"2025-06-06T11:05:39+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:05:39 +07] \"GET /shops/nearby?latitude=13.8090471\u0026longitude=100.7353852\u0026radius=50\u0026is_active=true\u0026limit=50 HTTP/1.1 500 116.377292ms \"curl/8.7.1\" \"\n","time":"2025-06-06T11:05:39+07:00"}
{"level":"info","msg":"Getting nearby shops: lat=13.811487, lng=100.733479, radius=10.000000","time":"2025-06-06T11:06:01+07:00"}
{"level":"info","msg":"Getting shops with filters: page=0, limit=50","time":"2025-06-06T11:06:01+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:06:01+07:00"}

2025/06/06 11:06:01 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [35;1mERROR: column "distance" does not exist (SQLSTATE 42703)
[0m[33m[40.726ms] [34;1m[rows:1][0m SELECT * FROM "shops" WHERE is_active = true AND 
			(6371 * acos(cos(radians(13.811487485769522)) * cos(radians(address_latitude)) *
			cos(radians(address_longitude) - radians(100.73347905607432)) +
			sin(radians(13.811487485769522)) * sin(radians(address_latitude)))) <= 10 AND "shops"."deleted_at" IS NULL ORDER BY distance DESC LIMIT 50
{"error":"failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get shops","time":"2025-06-06T11:06:01+07:00"}
{"error":"failed to get shops: failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get nearby shops","time":"2025-06-06T11:06:01+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:06:01 +07] \"GET /shops/nearby?latitude=13.811487485769522\u0026longitude=100.73347905607432\u0026radius=10\u0026is_active=true\u0026limit=50 HTTP/1.1 500 120.045792ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:06:01+07:00"}
{"level":"info","msg":"Getting nearby shops: lat=13.811487, lng=100.733479, radius=10.000000","time":"2025-06-06T11:06:13+07:00"}
{"level":"info","msg":"Getting shops with filters: page=0, limit=50","time":"2025-06-06T11:06:13+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:06:13+07:00"}

2025/06/06 11:06:13 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [35;1mERROR: column "distance" does not exist (SQLSTATE 42703)
[0m[33m[40.007ms] [34;1m[rows:1][0m SELECT * FROM "shops" WHERE is_active = true AND 
			(6371 * acos(cos(radians(13.811487485769522)) * cos(radians(address_latitude)) *
			cos(radians(address_longitude) - radians(100.73347905607432)) +
			sin(radians(13.811487485769522)) * sin(radians(address_latitude)))) <= 10 AND "shops"."deleted_at" IS NULL ORDER BY distance DESC LIMIT 50
{"error":"failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get shops","time":"2025-06-06T11:06:13+07:00"}
{"error":"failed to get shops: failed to get shops: ERROR: column \"distance\" does not exist (SQLSTATE 42703)","level":"error","msg":"Failed to get nearby shops","time":"2025-06-06T11:06:13+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:06:13 +07] \"GET /shops/nearby?latitude=13.811487485769522\u0026longitude=100.73347905607432\u0026radius=10\u0026is_active=true\u0026limit=50 HTTP/1.1 500 122.869375ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:06:13+07:00"}
{"all_query_params":{"is_active":["true"],"limit":["50"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T11:10:00+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T11:10:00+07:00"}
{"cuisine_type":null,"level":"info","limit":50,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"is_active=true\u0026limit=50","search":"","time":"2025-06-06T11:10:00+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=50","time":"2025-06-06T11:10:00+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T11:10:00+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:10:00 +07] \"GET /orders/customer?customerPhone=guest_1749180759622_hcxcuumd3\u0026limit=50 HTTP/1.1 200 111.319875ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:10:00+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:10:00 +07] \"GET /cart HTTP/1.1 200 183.949917ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:10:00+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:10:00+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T11:10:00+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 11:10:00 +07] \"GET /shops?is_active=true\u0026limit=50 HTTP/1.1 200 315.748209ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T11:10:00+07:00"}
